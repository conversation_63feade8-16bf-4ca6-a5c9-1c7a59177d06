interface Question {
  id: number;
  type: string;
  question: string;
  options: string[];
  correctAnswer: string;
  explanation?: string;
}

interface MathematicalProblemsProps {
  question: Question;
  selectedAnswer: string | undefined;
  onAnswerSelect: (answer: string) => void;
}

export default function MathematicalProblems({ question, selectedAnswer, onAnswerSelect }: MathematicalProblemsProps) {
  return (
    <div className="space-y-6">
      {/* Question Text */}
      <div className="text-lg text-gray-800 leading-relaxed">
        {question.question}
      </div>

      {/* Answer Options */}
      <div className="grid grid-cols-2 gap-3">
        {question.options.map((option, index) => {
          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
          const isSelected = selectedAnswer === optionLetter;
          
          return (
            <button
              key={index}
              onClick={() => onAnswerSelect(optionLetter)}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 text-blue-900'
                  : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center gap-3">
                <span className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                  isSelected
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700'
                }`}>
                  {optionLetter}
                </span>
                <span className="text-gray-800 font-mono text-lg">{option}</span>
              </div>
            </button>
          );
        })}
      </div>

      {/* Instructions */}
      <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
        <strong>Instructions:</strong> Solve the mathematical problem and select the correct answer.
      </div>
    </div>
  );
}
