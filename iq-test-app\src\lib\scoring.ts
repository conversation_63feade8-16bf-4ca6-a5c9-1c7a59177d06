interface TestAnswer {
  questionId: number;
  selectedAnswer: string;
  timeSpent?: number;
}

interface QuestionData {
  id: number;
  type: 'logical' | 'pattern' | 'mathematical' | 'verbal';
  correctAnswer: string;
  difficulty: 'easy' | 'medium' | 'hard';
  weight?: number;
}

interface ScoreBreakdown {
  totalScore: number;
  categoryScores: {
    logical: number;
    pattern: number;
    mathematical: number;
    verbal: number;
  };
  correctAnswers: number;
  totalQuestions: number;
  percentile: number;
  classification: string;
}

// Standard IQ distribution parameters
const IQ_MEAN = 100;
const IQ_STANDARD_DEVIATION = 15;

// Question weights by difficulty
const DIFFICULTY_WEIGHTS = {
  easy: 1.0,
  medium: 1.2,
  hard: 1.5
};

// Category weights (can be adjusted based on test design)
const CATEGORY_WEIGHTS = {
  logical: 1.0,
  pattern: 1.0,
  mathematical: 1.0,
  verbal: 1.0
};

/**
 * Calculate IQ score based on test answers
 */
export function calculateIQScore(
  answers: Record<number, string>,
  questions: QuestionData[],
  timeSpent?: number
): ScoreBreakdown {
  const categoryScores = {
    logical: 0,
    pattern: 0,
    mathematical: 0,
    verbal: 0
  };

  const categoryTotals = {
    logical: 0,
    pattern: 0,
    mathematical: 0,
    verbal: 0
  };

  let totalCorrect = 0;
  let totalPossibleScore = 0;

  // Calculate scores by category
  questions.forEach((question) => {
    const userAnswer = answers[question.id];
    const isCorrect = userAnswer === question.correctAnswer;
    const weight = DIFFICULTY_WEIGHTS[question.difficulty] * CATEGORY_WEIGHTS[question.type];
    
    categoryTotals[question.type] += weight;
    totalPossibleScore += weight;

    if (isCorrect) {
      categoryScores[question.type] += weight;
      totalCorrect++;
    }
  });

  // Convert to percentages
  const categoryPercentages = {
    logical: categoryTotals.logical > 0 ? (categoryScores.logical / categoryTotals.logical) * 100 : 0,
    pattern: categoryTotals.pattern > 0 ? (categoryScores.pattern / categoryTotals.pattern) * 100 : 0,
    mathematical: categoryTotals.mathematical > 0 ? (categoryScores.mathematical / categoryTotals.mathematical) * 100 : 0,
    verbal: categoryTotals.verbal > 0 ? (categoryScores.verbal / categoryTotals.verbal) * 100 : 0
  };

  // Calculate overall percentage
  const overallPercentage = totalPossibleScore > 0 ? 
    ((categoryScores.logical + categoryScores.pattern + categoryScores.mathematical + categoryScores.verbal) / totalPossibleScore) * 100 : 0;

  // Convert to IQ score using normal distribution
  const iqScore = calculateIQFromPercentage(overallPercentage);

  // Calculate percentile
  const percentile = calculatePercentile(iqScore);

  // Get classification
  const classification = getIQClassification(iqScore);

  return {
    totalScore: Math.round(iqScore),
    categoryScores: {
      logical: Math.round(categoryPercentages.logical),
      pattern: Math.round(categoryPercentages.pattern),
      mathematical: Math.round(categoryPercentages.mathematical),
      verbal: Math.round(categoryPercentages.verbal)
    },
    correctAnswers: totalCorrect,
    totalQuestions: questions.length,
    percentile: Math.round(percentile),
    classification
  };
}

/**
 * Convert percentage score to IQ score
 */
function calculateIQFromPercentage(percentage: number): number {
  // Convert percentage to z-score (assuming normal distribution)
  // This is a simplified model - real IQ tests use more sophisticated norming
  
  if (percentage >= 95) return 130 + (percentage - 95) * 2; // Very superior range
  if (percentage >= 85) return 115 + (percentage - 85) * 1.5; // Superior range
  if (percentage >= 70) return 100 + (percentage - 70) * 1; // Above average
  if (percentage >= 50) return 85 + (percentage - 50) * 0.75; // Average range
  if (percentage >= 30) return 70 + (percentage - 30) * 0.75; // Below average
  
  return Math.max(55, 70 - (30 - percentage) * 0.5); // Minimum reasonable score
}

/**
 * Calculate percentile from IQ score
 */
function calculatePercentile(iqScore: number): number {
  // Using cumulative normal distribution
  const zScore = (iqScore - IQ_MEAN) / IQ_STANDARD_DEVIATION;
  
  // Approximate cumulative normal distribution
  const percentile = cumulativeNormalDistribution(zScore) * 100;
  
  return Math.max(1, Math.min(99, percentile));
}

/**
 * Approximate cumulative normal distribution function
 */
function cumulativeNormalDistribution(z: number): number {
  // Abramowitz and Stegun approximation
  const t = 1 / (1 + 0.2316419 * Math.abs(z));
  const d = 0.3989423 * Math.exp(-z * z / 2);
  const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
  
  return z > 0 ? 1 - prob : prob;
}

/**
 * Get IQ classification based on score
 */
function getIQClassification(iqScore: number): string {
  if (iqScore >= 145) return 'Genius';
  if (iqScore >= 130) return 'Very Superior';
  if (iqScore >= 120) return 'Superior';
  if (iqScore >= 110) return 'High Average';
  if (iqScore >= 90) return 'Average';
  if (iqScore >= 80) return 'Low Average';
  if (iqScore >= 70) return 'Borderline';
  return 'Below Average';
}

/**
 * Generate detailed analysis of test performance
 */
export function generateAnalysis(scoreBreakdown: ScoreBreakdown): {
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
} {
  const { categoryScores } = scoreBreakdown;
  const categories = Object.entries(categoryScores);
  
  // Sort categories by performance
  categories.sort((a, b) => b[1] - a[1]);
  
  const strengths: string[] = [];
  const weaknesses: string[] = [];
  const recommendations: string[] = [];

  // Identify strengths (top 2 categories above 70%)
  categories.slice(0, 2).forEach(([category, score]) => {
    if (score >= 70) {
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      strengths.push(`${categoryName} reasoning (${score}%)`);
    }
  });

  // Identify weaknesses (bottom 2 categories below 60%)
  categories.slice(-2).forEach(([category, score]) => {
    if (score < 60) {
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      weaknesses.push(`${categoryName} reasoning (${score}%)`);
    }
  });

  // Generate recommendations
  if (categoryScores.logical < 60) {
    recommendations.push('Practice logical reasoning puzzles and deductive thinking exercises');
  }
  if (categoryScores.pattern < 60) {
    recommendations.push('Work on pattern recognition through visual puzzles and sequence problems');
  }
  if (categoryScores.mathematical < 60) {
    recommendations.push('Strengthen mathematical skills through regular practice with numerical problems');
  }
  if (categoryScores.verbal < 60) {
    recommendations.push('Expand vocabulary and reading comprehension through diverse reading materials');
  }

  // General recommendations
  if (scoreBreakdown.totalScore < 100) {
    recommendations.push('Consider taking practice tests to familiarize yourself with question formats');
    recommendations.push('Ensure adequate rest and focus during test-taking');
  }

  return { strengths, weaknesses, recommendations };
}

/**
 * Compare score with population norms
 */
export function getPopulationComparison(iqScore: number): {
  higherThan: number;
  description: string;
} {
  const percentile = calculatePercentile(iqScore);
  const higherThan = Math.round(percentile);
  
  let description = '';
  if (percentile >= 98) {
    description = 'You scored higher than 98% of the population - exceptional performance!';
  } else if (percentile >= 90) {
    description = 'You scored higher than 90% of the population - excellent performance!';
  } else if (percentile >= 75) {
    description = 'You scored higher than 75% of the population - above average performance.';
  } else if (percentile >= 50) {
    description = 'You scored higher than 50% of the population - average performance.';
  } else if (percentile >= 25) {
    description = 'You scored higher than 25% of the population - below average performance.';
  } else {
    description = 'Your score indicates room for improvement in cognitive skills.';
  }

  return { higherThan, description };
}
