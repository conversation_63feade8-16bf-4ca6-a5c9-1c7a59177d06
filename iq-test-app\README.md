# Professional IQ Test Application

A comprehensive, modern web application for evaluating and scoring IQ tests with detailed analytics and professional presentation.

## 🚀 Features

### Core Functionality
- **Comprehensive IQ Assessment**: 40 questions across 4 cognitive domains
- **Timed Testing**: 45-minute professional assessment with countdown timer
- **Multiple Question Types**:
  - Logical Reasoning
  - Pattern Recognition
  - Mathematical Problems
  - Verbal Comprehension
- **Advanced Scoring System**: Standardized IQ calculation with percentile ranking
- **Detailed Results Analysis**: Breakdown by category with strengths/weaknesses
- **Progress Tracking**: Visual progress indicators during test

### User Experience
- **Modern, Responsive Design**: Works seamlessly on desktop and mobile
- **Professional Interface**: Clean, intuitive navigation
- **Practice Mode**: Untimed practice with immediate feedback
- **Results Dashboard**: Historical test performance tracking
- **Export Capabilities**: Download results as text files

### Technical Features
- **Real-time Timer**: Accurate countdown with visual warnings
- **Local Storage**: Persistent test results and progress
- **Accessibility**: Keyboard navigation and screen reader support
- **Performance Optimized**: Fast loading with smooth animations

## 🛠️ Technology Stack

- **Frontend**: Next.js 14 with React 18
- **Styling**: Tailwind CSS for responsive design
- **Icons**: Lucide React for professional iconography
- **Typography**: Inter font for optimal readability
- **State Management**: React Context with useReducer
- **Data Persistence**: localStorage (ready for database integration)

## 📦 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd iq-test-app
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to `http://localhost:3000`

## 🏗️ Project Structure

```
iq-test-app/
├── src/
│   ├── app/                    # Next.js app router pages
│   │   ├── admin/             # Admin panel
│   │   ├── dashboard/         # User dashboard
│   │   ├── practice/          # Practice mode
│   │   ├── results/[id]/      # Test results page
│   │   ├── test/              # Main test interface
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Landing page
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable components
│   │   ├── QuestionTypes/     # Question type components
│   │   ├── Navigation.tsx     # Site navigation
│   │   ├── ProgressBar.tsx    # Progress indicator
│   │   ├── ScoreDisplay.tsx   # Score visualization
│   │   ├── TestInterface.tsx  # Main test component
│   │   └── Timer.tsx          # Countdown timer
│   └── lib/                   # Utility libraries
│       ├── questions.ts       # Question bank
│       └── scoring.ts         # IQ calculation algorithms
├── public/                    # Static assets
├── package.json              # Dependencies
└── README.md                 # Documentation
```

## 🧠 IQ Scoring System

The application uses a sophisticated scoring algorithm that:

- **Weights questions** by difficulty (easy: 1.0, medium: 1.2, hard: 1.5)
- **Balances categories** equally across cognitive domains
- **Calculates percentiles** using normal distribution
- **Provides classifications** from "Below Average" to "Genius"
- **Generates insights** on strengths and improvement areas

### Score Ranges
- **130+**: Very Superior (Genius level)
- **120-129**: Superior
- **110-119**: High Average
- **90-109**: Average
- **80-89**: Low Average
- **Below 80**: Below Average

## 📊 Question Categories

### Logical Reasoning
- Deductive reasoning problems
- Syllogistic logic
- Conditional statements
- Pattern-based logic

### Pattern Recognition
- Number sequences
- Visual patterns
- Geometric progressions
- Abstract reasoning

### Mathematical Problems
- Arithmetic operations
- Algebraic equations
- Percentage calculations
- Word problems

### Verbal Comprehension
- Vocabulary assessment
- Reading comprehension
- Analogies
- Language reasoning

## 🎯 Usage Guide

### Taking a Test
1. Click "Start IQ Test" from the homepage
2. Read the instructions carefully
3. Complete 40 questions within 45 minutes
4. Review your answers before submitting
5. View detailed results and analysis

### Practice Mode
1. Select "Practice Mode" from navigation
2. Choose a specific category to focus on
3. Practice without time pressure
4. Get immediate feedback on answers

### Dashboard
1. View your test history
2. Track performance over time
3. Compare scores across attempts
4. Export results for records

## 🔧 Customization

### Adding Questions
Edit `src/lib/questions.ts` to add new questions:

```typescript
{
  id: 41,
  type: 'logical',
  question: 'Your question here...',
  options: ['A', 'B', 'C', 'D'],
  correctAnswer: 'A',
  explanation: 'Explanation here...'
}
```

### Modifying Scoring
Adjust scoring parameters in `src/lib/scoring.ts`:

```typescript
const DIFFICULTY_WEIGHTS = {
  easy: 1.0,
  medium: 1.2,
  hard: 1.5
};
```

### Styling Changes
Modify Tailwind classes in components or update `src/app/globals.css` for global styles.

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Other Platforms
```bash
npm run build
npm start
```

## 🔮 Future Enhancements

### Planned Features
- **User Authentication**: Account creation and login
- **Database Integration**: Supabase for data persistence
- **Advanced Analytics**: Detailed performance metrics
- **Adaptive Testing**: Dynamic difficulty adjustment
- **Social Features**: Score sharing and comparisons
- **Mobile App**: React Native version
- **Multilingual Support**: Multiple language options

### Database Schema (Supabase Ready)
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE,
  created_at TIMESTAMP
);

-- Test sessions
CREATE TABLE test_sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  score INTEGER,
  completed_at TIMESTAMP,
  time_spent INTEGER
);

-- Questions bank
CREATE TABLE questions (
  id SERIAL PRIMARY KEY,
  type TEXT,
  question TEXT,
  options JSONB,
  correct_answer TEXT,
  difficulty TEXT
);
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with Next.js and React
- Styled with Tailwind CSS
- Icons by Lucide
- Inspired by professional IQ assessment standards

---

**Note**: This application is for educational and entertainment purposes. For official IQ testing, consult with qualified professionals.
