'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import TestInterface from '@/components/TestInterface';
import Timer from '@/components/Timer';
import ProgressBar from '@/components/ProgressBar';
import { Brain, Clock, AlertCircle } from 'lucide-react';

export default function TestPage() {
  const [testStarted, setTestStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [timeRemaining, setTimeRemaining] = useState(45 * 60); // 45 minutes
  const router = useRouter();

  const totalQuestions = 40;
  const testDuration = 45 * 60; // 45 minutes in seconds

  const handleStartTest = () => {
    setTestStarted(true);
  };

  const handleAnswerSelect = (questionId: number, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestion < totalQuestions - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const handleSubmitTest = () => {
    // Calculate score and redirect to results
    const score = calculateScore(answers);
    const testId = Date.now().toString();
    
    // Store results in localStorage for now (later we'll use Supabase)
    localStorage.setItem(`test_${testId}`, JSON.stringify({
      answers,
      score,
      completedAt: new Date().toISOString(),
      timeSpent: testDuration - timeRemaining
    }));
    
    router.push(`/results/${testId}`);
  };

  const calculateScore = (answers: Record<number, string>) => {
    // Simplified scoring logic - in a real app this would be more sophisticated
    const correctAnswers = Object.keys(answers).length;
    const percentage = (correctAnswers / totalQuestions) * 100;
    return Math.round(85 + (percentage - 50) * 0.6); // Basic IQ calculation
  };

  const handleTimeUp = () => {
    handleSubmitTest();
  };

  if (!testStarted) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <Brain className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">IQ Test Instructions</h1>
            <p className="text-lg text-gray-600">
              You are about to take a comprehensive IQ assessment
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="bg-blue-50 p-6 rounded-lg">
              <Clock className="h-8 w-8 text-blue-600 mb-3" />
              <h3 className="font-semibold mb-2">Time Limit</h3>
              <p className="text-gray-700">45 minutes to complete 40 questions</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg">
              <AlertCircle className="h-8 w-8 text-green-600 mb-3" />
              <h3 className="font-semibold mb-2">Instructions</h3>
              <p className="text-gray-700">Choose the best answer for each question</p>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-yellow-800 mb-2">Important Notes:</h3>
            <ul className="list-disc list-inside text-yellow-700 space-y-1">
              <li>You cannot pause the test once started</li>
              <li>Questions cover logical reasoning, math, verbal, and pattern recognition</li>
              <li>Work quickly but carefully</li>
              <li>You can review and change answers before submitting</li>
            </ul>
          </div>

          <div className="text-center">
            <button
              onClick={handleStartTest}
              className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
            >
              <Brain className="h-5 w-5" />
              Start Test
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Test Header */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-900">IQ Test</h1>
          <Timer 
            timeRemaining={timeRemaining} 
            onTimeUp={handleTimeUp}
            onTimeUpdate={setTimeRemaining}
          />
        </div>
        <ProgressBar 
          current={currentQuestion + 1} 
          total={totalQuestions} 
        />
      </div>

      {/* Test Interface */}
      <TestInterface
        questionNumber={currentQuestion + 1}
        totalQuestions={totalQuestions}
        selectedAnswer={answers[currentQuestion]}
        onAnswerSelect={(answer) => handleAnswerSelect(currentQuestion, answer)}
        onNext={handleNextQuestion}
        onPrevious={handlePreviousQuestion}
        onSubmit={handleSubmitTest}
        canGoNext={currentQuestion < totalQuestions - 1}
        canGoPrevious={currentQuestion > 0}
        isLastQuestion={currentQuestion === totalQuestions - 1}
      />
    </div>
  );
}
