'use client';

import { useEffect } from 'react';
import { Clock, AlertTriangle } from 'lucide-react';

interface TimerProps {
  timeRemaining: number;
  onTimeUp: () => void;
  onTimeUpdate: (time: number) => void;
}

export default function Timer({ timeRemaining, onTimeUp, onTimeUpdate }: TimerProps) {
  useEffect(() => {
    if (timeRemaining <= 0) {
      onTimeUp();
      return;
    }

    const timer = setInterval(() => {
      onTimeUpdate(timeRemaining - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining, onTimeUp, onTimeUpdate]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isLowTime = timeRemaining <= 300; // 5 minutes
  const isCriticalTime = timeRemaining <= 60; // 1 minute

  return (
    <div className={`flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-semibold ${
      isCriticalTime 
        ? 'bg-red-100 text-red-700 border border-red-300' 
        : isLowTime 
        ? 'bg-yellow-100 text-yellow-700 border border-yellow-300'
        : 'bg-blue-100 text-blue-700 border border-blue-300'
    }`}>
      {isCriticalTime ? (
        <AlertTriangle className="h-5 w-5 animate-pulse" />
      ) : (
        <Clock className="h-5 w-5" />
      )}
      <span>{formatTime(timeRemaining)}</span>
    </div>
  );
}
