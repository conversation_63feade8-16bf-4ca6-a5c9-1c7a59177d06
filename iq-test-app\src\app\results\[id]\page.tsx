'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Brain, Clock, BarChart3, Download, Home, RotateCcw } from 'lucide-react';
import ScoreDisplay from '@/components/ScoreDisplay';

interface TestResult {
  answers: Record<number, string>;
  score: number;
  completedAt: string;
  timeSpent: number;
}

export default function ResultsPage() {
  const params = useParams();
  const router = useRouter();
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const testId = params.id as string;
    const storedResult = localStorage.getItem(`test_${testId}`);
    
    if (storedResult) {
      setTestResult(JSON.parse(storedResult));
    } else {
      // Redirect to home if no result found
      router.push('/');
    }
    setLoading(false);
  }, [params.id, router]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getScoreInterpretation = (score: number) => {
    if (score >= 130) return { level: 'Very Superior', description: 'Exceptional intellectual ability', color: 'text-purple-600' };
    if (score >= 120) return { level: 'Superior', description: 'Well above average intelligence', color: 'text-blue-600' };
    if (score >= 110) return { level: 'High Average', description: 'Above average intelligence', color: 'text-green-600' };
    if (score >= 90) return { level: 'Average', description: 'Average intellectual ability', color: 'text-gray-600' };
    if (score >= 80) return { level: 'Low Average', description: 'Below average intelligence', color: 'text-yellow-600' };
    return { level: 'Below Average', description: 'Well below average intelligence', color: 'text-red-600' };
  };

  const downloadResults = () => {
    if (!testResult) return;
    
    const interpretation = getScoreInterpretation(testResult.score);
    const resultsText = `
IQ Test Results
===============
Score: ${testResult.score}
Classification: ${interpretation.level}
Description: ${interpretation.description}
Time Spent: ${formatTime(testResult.timeSpent)}
Completed: ${new Date(testResult.completedAt).toLocaleString()}
Questions Answered: ${Object.keys(testResult.answers).length}/40
    `;
    
    const blob = new Blob([resultsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `iq-test-results-${params.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-6"></div>
            <div className="h-32 bg-gray-200 rounded mb-6"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!testResult) {
    return (
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Results Not Found</h1>
          <p className="text-gray-600 mb-6">The test results you're looking for could not be found.</p>
          <Link
            href="/"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Return Home
          </Link>
        </div>
      </div>
    );
  }

  const interpretation = getScoreInterpretation(testResult.score);
  const questionsAnswered = Object.keys(testResult.answers).length;
  const completionRate = (questionsAnswered / 40) * 100;

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <Brain className="h-16 w-16 text-blue-600 mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Your IQ Test Results</h1>
        <p className="text-gray-600">Completed on {new Date(testResult.completedAt).toLocaleDateString()}</p>
      </div>

      {/* Score Display */}
      <div className="grid md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">IQ Score</h2>
          <ScoreDisplay score={testResult.score} />
          <div className="mt-6">
            <h3 className={`text-xl font-semibold ${interpretation.color} mb-2`}>
              {interpretation.level}
            </h3>
            <p className="text-gray-600">{interpretation.description}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Test Statistics</h2>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Questions Answered:</span>
              <span className="font-semibold">{questionsAnswered}/40</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Completion Rate:</span>
              <span className="font-semibold">{completionRate.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Time Spent:</span>
              <span className="font-semibold">{formatTime(testResult.timeSpent)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Average per Question:</span>
              <span className="font-semibold">{Math.round(testResult.timeSpent / questionsAnswered)}s</span>
            </div>
          </div>
        </div>
      </div>

      {/* Score Breakdown */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Score Interpretation</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">IQ Score Ranges</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>130+</span>
                <span className="text-purple-600 font-medium">Very Superior</span>
              </div>
              <div className="flex justify-between">
                <span>120-129</span>
                <span className="text-blue-600 font-medium">Superior</span>
              </div>
              <div className="flex justify-between">
                <span>110-119</span>
                <span className="text-green-600 font-medium">High Average</span>
              </div>
              <div className="flex justify-between">
                <span>90-109</span>
                <span className="text-gray-600 font-medium">Average</span>
              </div>
              <div className="flex justify-between">
                <span>80-89</span>
                <span className="text-yellow-600 font-medium">Low Average</span>
              </div>
              <div className="flex justify-between">
                <span>Below 80</span>
                <span className="text-red-600 font-medium">Below Average</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Performance</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-700 leading-relaxed">
                Your IQ score of <strong>{testResult.score}</strong> places you in the{' '}
                <span className={interpretation.color}>
                  <strong>{interpretation.level}</strong>
                </span>{' '}
                range. {interpretation.description}. This score is based on your performance 
                across multiple cognitive domains including logical reasoning, pattern recognition, 
                mathematical problems, and verbal comprehension.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">What's Next?</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={downloadResults}
            className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download Results
          </button>
          <Link
            href="/test"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Take Another Test
          </Link>
          <Link
            href="/dashboard"
            className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            View Dashboard
          </Link>
          <Link
            href="/"
            className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Home
          </Link>
        </div>
      </div>
    </div>
  );
}
