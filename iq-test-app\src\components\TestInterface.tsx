'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Send } from 'lucide-react';
import LogicalReasoning from './QuestionTypes/LogicalReasoning';
import PatternRecognition from './QuestionTypes/PatternRecognition';
import MathematicalProblems from './QuestionTypes/MathematicalProblems';
import VerbalComprehension from './QuestionTypes/VerbalComprehension';
import { getQuestionData } from '@/lib/questions';

interface TestInterfaceProps {
  questionNumber: number;
  totalQuestions: number;
  selectedAnswer: string | undefined;
  onAnswerSelect: (answer: string) => void;
  onNext: () => void;
  onPrevious: () => void;
  onSubmit: () => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isLastQuestion: boolean;
}

export default function TestInterface({
  questionNumber,
  totalQuestions,
  selectedAnswer,
  onAnswerSelect,
  onNext,
  onPrevious,
  onSubmit,
  canGoNext,
  canGoPrevious,
  isLastQuestion
}: TestInterfaceProps) {
  const [questionData, setQuestionData] = useState<any>(null);

  useEffect(() => {
    const data = getQuestionData(questionNumber);
    setQuestionData(data);
  }, [questionNumber]);

  if (!questionData) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const renderQuestionComponent = () => {
    switch (questionData.type) {
      case 'logical':
        return (
          <LogicalReasoning
            question={questionData}
            selectedAnswer={selectedAnswer}
            onAnswerSelect={onAnswerSelect}
          />
        );
      case 'pattern':
        return (
          <PatternRecognition
            question={questionData}
            selectedAnswer={selectedAnswer}
            onAnswerSelect={onAnswerSelect}
          />
        );
      case 'mathematical':
        return (
          <MathematicalProblems
            question={questionData}
            selectedAnswer={selectedAnswer}
            onAnswerSelect={onAnswerSelect}
          />
        );
      case 'verbal':
        return (
          <VerbalComprehension
            question={questionData}
            selectedAnswer={selectedAnswer}
            onAnswerSelect={onAnswerSelect}
          />
        );
      default:
        return <div>Unknown question type</div>;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-8">
      {/* Question Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Question {questionNumber}
          </h2>
          <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            {questionData.type.charAt(0).toUpperCase() + questionData.type.slice(1)} Reasoning
          </span>
        </div>
      </div>

      {/* Question Content */}
      <div className="mb-8">
        {renderQuestionComponent()}
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t">
        <button
          onClick={onPrevious}
          disabled={!canGoPrevious}
          className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
            canGoPrevious
              ? 'text-gray-700 bg-gray-100 hover:bg-gray-200'
              : 'text-gray-400 bg-gray-50 cursor-not-allowed'
          }`}
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </button>

        <div className="text-sm text-gray-500">
          {selectedAnswer ? (
            <span className="text-green-600 font-medium">Answer selected</span>
          ) : (
            <span>Select an answer to continue</span>
          )}
        </div>

        {isLastQuestion ? (
          <button
            onClick={onSubmit}
            className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            <Send className="h-4 w-4" />
            Submit Test
          </button>
        ) : (
          <button
            onClick={onNext}
            disabled={!canGoNext}
            className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
              canGoNext
                ? 'text-white bg-blue-600 hover:bg-blue-700'
                : 'text-gray-400 bg-gray-50 cursor-not-allowed'
            }`}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}
