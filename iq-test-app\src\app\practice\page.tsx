'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Brain, Play, BookOpen, Target, Clock } from 'lucide-react';
import { getQuestionsByType } from '@/lib/questions';

export default function PracticePage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories = [
    {
      id: 'logical',
      name: 'Logical Reasoning',
      description: 'Practice logical deduction and reasoning problems',
      icon: Brain,
      color: 'blue',
      questionCount: getQuestionsByType('logical').length
    },
    {
      id: 'pattern',
      name: 'Pattern Recognition',
      description: 'Identify patterns and sequences in visual and numerical data',
      icon: Target,
      color: 'green',
      questionCount: getQuestionsByType('pattern').length
    },
    {
      id: 'mathematical',
      name: 'Mathematical Problems',
      description: 'Solve numerical reasoning and mathematical concepts',
      icon: BookOpen,
      color: 'purple',
      questionCount: getQuestionsByType('mathematical').length
    },
    {
      id: 'verbal',
      name: 'Verbal Comprehension',
      description: 'Test vocabulary, reading comprehension, and verbal reasoning',
      icon: Clock,
      color: 'orange',
      questionCount: getQuestionsByType('verbal').length
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100',
      green: 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100',
      purple: 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100',
      orange: 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex justify-center mb-6">
          <Play className="h-16 w-16 text-blue-600" />
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Practice Mode</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Sharpen your cognitive skills with practice questions from each category. 
          No time limits, with immediate feedback and explanations.
        </p>
      </div>

      {/* Practice Categories */}
      <div className="grid md:grid-cols-2 gap-6 mb-12">
        {categories.map((category) => {
          const Icon = category.icon;
          return (
            <div
              key={category.id}
              className={`p-8 rounded-lg border-2 transition-all duration-200 cursor-pointer ${getColorClasses(category.color)}`}
              onClick={() => setSelectedCategory(category.id)}
            >
              <div className="flex items-start gap-4">
                <Icon className="h-12 w-12 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">{category.name}</h3>
                  <p className="text-sm opacity-80 mb-4">{category.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      {category.questionCount} practice questions
                    </span>
                    <button className="bg-white bg-opacity-50 px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-75 transition-colors">
                      Start Practice
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Features */}
      <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Practice Mode Features</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="font-semibold mb-2">No Time Pressure</h3>
            <p className="text-sm text-gray-600">Take your time to think through each question carefully</p>
          </div>
          <div className="text-center">
            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-semibold mb-2">Instant Feedback</h3>
            <p className="text-sm text-gray-600">Get immediate explanations for correct and incorrect answers</p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Target className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="font-semibold mb-2">Targeted Practice</h3>
            <p className="text-sm text-gray-600">Focus on specific question types to improve weak areas</p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
        <h2 className="text-2xl font-bold mb-4">Ready for the Real Test?</h2>
        <p className="text-lg mb-6 opacity-90">
          Once you've practiced enough, take the full timed IQ assessment
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/test"
            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center gap-2"
          >
            <Brain className="h-5 w-5" />
            Take Full Test
          </Link>
          <Link
            href="/"
            className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:bg-opacity-10 transition-colors inline-flex items-center justify-center gap-2"
          >
            Learn More
          </Link>
        </div>
      </div>
    </div>
  );
}
