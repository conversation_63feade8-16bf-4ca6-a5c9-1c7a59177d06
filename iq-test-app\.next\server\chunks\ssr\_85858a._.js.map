{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/QuestionTypes/LogicalReasoning.tsx"], "sourcesContent": ["interface Question {\n  id: number;\n  type: string;\n  question: string;\n  options: string[];\n  correctAnswer: string;\n  explanation?: string;\n}\n\ninterface LogicalReasoningProps {\n  question: Question;\n  selectedAnswer: string | undefined;\n  onAnswerSelect: (answer: string) => void;\n}\n\nexport default function LogicalReasoning({ question, selectedAnswer, onAnswerSelect }: LogicalReasoningProps) {\n  return (\n    <div className=\"space-y-6\">\n      {/* Question Text */}\n      <div className=\"text-lg text-gray-800 leading-relaxed\">\n        {question.question}\n      </div>\n\n      {/* Answer Options */}\n      <div className=\"space-y-3\">\n        {question.options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n          const isSelected = selectedAnswer === optionLetter;\n          \n          return (\n            <button\n              key={index}\n              onClick={() => onAnswerSelect(optionLetter)}\n              className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900'\n                  : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-3\">\n                <span className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-200 text-gray-700'\n                }`}>\n                  {optionLetter}\n                </span>\n                <span className=\"text-gray-800\">{option}</span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg\">\n        <strong>Instructions:</strong> Choose the answer that best completes the logical sequence or solves the reasoning problem.\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAee,SAAS,iBAAiB,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAyB;IAC1G,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,SAAS,QAAQ;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;oBAC7B,MAAM,eAAe,OAAO,YAAY,CAAC,KAAK,QAAQ,aAAa;oBACnE,MAAM,aAAa,mBAAmB;oBAEtC,qBACE,8OAAC;wBAEC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,qEAAqE,EAC/E,aACI,6CACA,mEACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,0FAA0F,EAC1G,aACI,2BACA,6BACJ;8CACC;;;;;;8CAEH,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;uBAhB9B;;;;;gBAoBX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAO;;;;;;oBAAsB;;;;;;;;;;;;;AAItC"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/QuestionTypes/PatternRecognition.tsx"], "sourcesContent": ["interface Question {\n  id: number;\n  type: string;\n  question: string;\n  options: string[];\n  correctAnswer: string;\n  explanation?: string;\n  pattern?: string[];\n}\n\ninterface PatternRecognitionProps {\n  question: Question;\n  selectedAnswer: string | undefined;\n  onAnswerSelect: (answer: string) => void;\n}\n\nexport default function PatternRecognition({ question, selectedAnswer, onAnswerSelect }: PatternRecognitionProps) {\n  return (\n    <div className=\"space-y-6\">\n      {/* Question Text */}\n      <div className=\"text-lg text-gray-800 leading-relaxed\">\n        {question.question}\n      </div>\n\n      {/* Pattern Display (if available) */}\n      {question.pattern && (\n        <div className=\"bg-gray-50 p-6 rounded-lg\">\n          <div className=\"flex items-center justify-center space-x-4\">\n            {question.pattern.map((item, index) => (\n              <div key={index} className=\"flex items-center\">\n                <div className=\"w-16 h-16 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center text-2xl font-bold\">\n                  {item === '?' ? (\n                    <span className=\"text-blue-600\">?</span>\n                  ) : (\n                    <span>{item}</span>\n                  )}\n                </div>\n                {index < question.pattern.length - 1 && (\n                  <div className=\"mx-2 text-gray-400\">→</div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Answer Options */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {question.options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n          const isSelected = selectedAnswer === optionLetter;\n          \n          return (\n            <button\n              key={index}\n              onClick={() => onAnswerSelect(optionLetter)}\n              className={`p-4 rounded-lg border-2 transition-all duration-200 ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900'\n                  : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-center gap-3\">\n                <span className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-200 text-gray-700'\n                }`}>\n                  {optionLetter}\n                </span>\n                <span className=\"text-gray-800 font-mono text-lg\">{option}</span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg\">\n        <strong>Instructions:</strong> Identify the pattern and select the option that best continues the sequence.\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAgBe,SAAS,mBAAmB,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAA2B;IAC9G,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,SAAS,QAAQ;;;;;;YAInB,SAAS,OAAO,kBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,oBACR,8OAAC;wCAAK,WAAU;kDAAgB;;;;;6DAEhC,8OAAC;kDAAM;;;;;;;;;;;gCAGV,QAAQ,SAAS,OAAO,CAAC,MAAM,GAAG,mBACjC,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;;2BAT9B;;;;;;;;;;;;;;;0BAkBlB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;oBAC7B,MAAM,eAAe,OAAO,YAAY,CAAC,KAAK,QAAQ,aAAa;oBACnE,MAAM,aAAa,mBAAmB;oBAEtC,qBACE,8OAAC;wBAEC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,oDAAoD,EAC9D,aACI,6CACA,mEACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,0FAA0F,EAC1G,aACI,2BACA,6BACJ;8CACC;;;;;;8CAEH,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;uBAhBhD;;;;;gBAoBX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAO;;;;;;oBAAsB;;;;;;;;;;;;;AAItC"}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/QuestionTypes/MathematicalProblems.tsx"], "sourcesContent": ["interface Question {\n  id: number;\n  type: string;\n  question: string;\n  options: string[];\n  correctAnswer: string;\n  explanation?: string;\n}\n\ninterface MathematicalProblemsProps {\n  question: Question;\n  selectedAnswer: string | undefined;\n  onAnswerSelect: (answer: string) => void;\n}\n\nexport default function MathematicalProblems({ question, selectedAnswer, onAnswerSelect }: MathematicalProblemsProps) {\n  return (\n    <div className=\"space-y-6\">\n      {/* Question Text */}\n      <div className=\"text-lg text-gray-800 leading-relaxed\">\n        {question.question}\n      </div>\n\n      {/* Answer Options */}\n      <div className=\"grid grid-cols-2 gap-3\">\n        {question.options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n          const isSelected = selectedAnswer === optionLetter;\n          \n          return (\n            <button\n              key={index}\n              onClick={() => onAnswerSelect(optionLetter)}\n              className={`p-4 rounded-lg border-2 transition-all duration-200 ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900'\n                  : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-center gap-3\">\n                <span className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-200 text-gray-700'\n                }`}>\n                  {optionLetter}\n                </span>\n                <span className=\"text-gray-800 font-mono text-lg\">{option}</span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg\">\n        <strong>Instructions:</strong> Solve the mathematical problem and select the correct answer.\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAee,SAAS,qBAAqB,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAA6B;IAClH,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,SAAS,QAAQ;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;oBAC7B,MAAM,eAAe,OAAO,YAAY,CAAC,KAAK,QAAQ,aAAa;oBACnE,MAAM,aAAa,mBAAmB;oBAEtC,qBACE,8OAAC;wBAEC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,oDAAoD,EAC9D,aACI,6CACA,mEACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,0FAA0F,EAC1G,aACI,2BACA,6BACJ;8CACC;;;;;;8CAEH,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;uBAhBhD;;;;;gBAoBX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAO;;;;;;oBAAsB;;;;;;;;;;;;;AAItC"}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/QuestionTypes/VerbalComprehension.tsx"], "sourcesContent": ["interface Question {\n  id: number;\n  type: string;\n  question: string;\n  options: string[];\n  correctAnswer: string;\n  explanation?: string;\n  passage?: string;\n}\n\ninterface VerbalComprehensionProps {\n  question: Question;\n  selectedAnswer: string | undefined;\n  onAnswerSelect: (answer: string) => void;\n}\n\nexport default function VerbalComprehension({ question, selectedAnswer, onAnswerSelect }: VerbalComprehensionProps) {\n  return (\n    <div className=\"space-y-6\">\n      {/* Passage (if available) */}\n      {question.passage && (\n        <div className=\"bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500\">\n          <h4 className=\"font-semibold text-gray-900 mb-3\">Reading Passage:</h4>\n          <p className=\"text-gray-800 leading-relaxed\">{question.passage}</p>\n        </div>\n      )}\n\n      {/* Question Text */}\n      <div className=\"text-lg text-gray-800 leading-relaxed\">\n        {question.question}\n      </div>\n\n      {/* Answer Options */}\n      <div className=\"space-y-3\">\n        {question.options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n          const isSelected = selectedAnswer === optionLetter;\n          \n          return (\n            <button\n              key={index}\n              onClick={() => onAnswerSelect(optionLetter)}\n              className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900'\n                  : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-3\">\n                <span className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-200 text-gray-700'\n                }`}>\n                  {optionLetter}\n                </span>\n                <span className=\"text-gray-800\">{option}</span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg\">\n        <strong>Instructions:</strong> Choose the answer that best demonstrates understanding of the vocabulary, reading comprehension, or verbal reasoning.\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAgBe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAA4B;IAChH,qBACE,8OAAC;QAAI,WAAU;;YAEZ,SAAS,OAAO,kBACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAiC,SAAS,OAAO;;;;;;;;;;;;0BAKlE,8OAAC;gBAAI,WAAU;0BACZ,SAAS,QAAQ;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;oBAC7B,MAAM,eAAe,OAAO,YAAY,CAAC,KAAK,QAAQ,aAAa;oBACnE,MAAM,aAAa,mBAAmB;oBAEtC,qBACE,8OAAC;wBAEC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,qEAAqE,EAC/E,aACI,6CACA,mEACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,0FAA0F,EAC1G,aACI,2BACA,6BACJ;8CACC;;;;;;8CAEH,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;uBAhB9B;;;;;gBAoBX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAO;;;;;;oBAAsB;;;;;;;;;;;;;AAItC"}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/lib/questions.ts"], "sourcesContent": ["interface Question {\n  id: number;\n  type: 'logical' | 'pattern' | 'mathematical' | 'verbal';\n  question: string;\n  options: string[];\n  correctAnswer: string;\n  explanation?: string;\n  pattern?: string[];\n  passage?: string;\n}\n\nconst questionBank: Question[] = [\n  // Logical Reasoning Questions\n  {\n    id: 1,\n    type: 'logical',\n    question: 'All roses are flowers. Some flowers are red. Therefore:',\n    options: [\n      'All roses are red',\n      'Some roses may be red',\n      'No roses are red',\n      'All red things are roses'\n    ],\n    correctAnswer: 'B',\n    explanation: 'The conclusion follows logically from the premises.'\n  },\n  {\n    id: 2,\n    type: 'logical',\n    question: 'If all cats are mammals and all mammals are animals, then:',\n    options: [\n      'All animals are cats',\n      'All cats are animals',\n      'Some animals are not mammals',\n      'No cats are animals'\n    ],\n    correctAnswer: 'B'\n  },\n\n  // Pattern Recognition Questions\n  {\n    id: 3,\n    type: 'pattern',\n    question: 'What comes next in this sequence?',\n    pattern: ['2', '4', '8', '16', '?'],\n    options: ['24', '32', '20', '18'],\n    correctAnswer: 'B',\n    explanation: 'Each number is doubled: 2×2=4, 4×2=8, 8×2=16, 16×2=32'\n  },\n  {\n    id: 4,\n    type: 'pattern',\n    question: 'Complete the pattern:',\n    pattern: ['A', 'C', 'E', 'G', '?'],\n    options: ['H', 'I', 'J', 'K'],\n    correctAnswer: 'B',\n    explanation: 'Skip one letter each time: A(skip B)C(skip D)E(skip F)G(skip H)I'\n  },\n\n  // Mathematical Problems\n  {\n    id: 5,\n    type: 'mathematical',\n    question: 'If x + 5 = 12, what is the value of 2x?',\n    options: ['7', '14', '10', '12'],\n    correctAnswer: 'B',\n    explanation: 'x = 7, so 2x = 14'\n  },\n  {\n    id: 6,\n    type: 'mathematical',\n    question: 'What is 15% of 200?',\n    options: ['25', '30', '35', '40'],\n    correctAnswer: 'B',\n    explanation: '15% of 200 = 0.15 × 200 = 30'\n  },\n\n  // Verbal Comprehension Questions\n  {\n    id: 7,\n    type: 'verbal',\n    question: 'Which word is most similar in meaning to \"abundant\"?',\n    options: ['Scarce', 'Plentiful', 'Moderate', 'Limited'],\n    correctAnswer: 'B',\n    explanation: 'Abundant means existing in large quantities; plentiful.'\n  },\n  {\n    id: 8,\n    type: 'verbal',\n    passage: 'The rapid advancement of technology has transformed how we communicate, work, and live. While these changes bring many benefits, they also present new challenges that society must address.',\n    question: 'Based on the passage, technological advancement:',\n    options: [\n      'Only brings benefits',\n      'Only creates problems',\n      'Has both positive and negative effects',\n      'Should be avoided'\n    ],\n    correctAnswer: 'C'\n  }\n];\n\n// Generate more questions to reach 40 total\nconst generateAdditionalQuestions = (): Question[] => {\n  const additionalQuestions: Question[] = [];\n  let id = 9;\n\n  // Add more logical reasoning questions\n  for (let i = 0; i < 8; i++) {\n    additionalQuestions.push({\n      id: id++,\n      type: 'logical',\n      question: `Logical reasoning question ${i + 3}: If A implies B, and B implies C, what can we conclude about A and C?`,\n      options: ['A implies C', 'C implies A', 'A and C are unrelated', 'Cannot determine'],\n      correctAnswer: 'A'\n    });\n  }\n\n  // Add more pattern recognition questions\n  for (let i = 0; i < 8; i++) {\n    const sequence = [1 + i * 2, 3 + i * 2, 5 + i * 2, 7 + i * 2];\n    additionalQuestions.push({\n      id: id++,\n      type: 'pattern',\n      question: 'What is the next number in the sequence?',\n      pattern: sequence.map(n => n.toString()).concat(['?']),\n      options: [(sequence[3] + 2).toString(), (sequence[3] + 3).toString(), (sequence[3] + 1).toString(), (sequence[3] + 4).toString()],\n      correctAnswer: 'A'\n    });\n  }\n\n  // Add more mathematical problems\n  for (let i = 0; i < 8; i++) {\n    const a = 5 + i;\n    const b = 3 + i;\n    additionalQuestions.push({\n      id: id++,\n      type: 'mathematical',\n      question: `What is ${a} × ${b}?`,\n      options: [(a * b).toString(), (a * b + 1).toString(), (a * b - 1).toString(), (a * b + 2).toString()],\n      correctAnswer: 'A'\n    });\n  }\n\n  // Add more verbal comprehension questions\n  for (let i = 0; i < 8; i++) {\n    const words = ['Eloquent', 'Meticulous', 'Pragmatic', 'Tenacious', 'Versatile', 'Astute', 'Candid', 'Diligent'];\n    const synonyms = ['Articulate', 'Careful', 'Practical', 'Persistent', 'Adaptable', 'Clever', 'Honest', 'Hardworking'];\n    \n    additionalQuestions.push({\n      id: id++,\n      type: 'verbal',\n      question: `Which word is most similar in meaning to \"${words[i]}\"?`,\n      options: [synonyms[i], 'Opposite', 'Unrelated', 'Different'],\n      correctAnswer: 'A'\n    });\n  }\n\n  return additionalQuestions;\n};\n\nconst allQuestions = [...questionBank, ...generateAdditionalQuestions()];\n\nexport const getQuestionData = (questionNumber: number): Question => {\n  // Return question based on number (1-indexed)\n  const questionIndex = questionNumber - 1;\n  if (questionIndex >= 0 && questionIndex < allQuestions.length) {\n    return allQuestions[questionIndex];\n  }\n  \n  // Fallback question if index is out of bounds\n  return {\n    id: questionNumber,\n    type: 'logical',\n    question: 'Sample question for testing purposes.',\n    options: ['Option A', 'Option B', 'Option C', 'Option D'],\n    correctAnswer: 'A'\n  };\n};\n\nexport const getTotalQuestions = (): number => {\n  return allQuestions.length;\n};\n\nexport const getQuestionsByType = (type: Question['type']): Question[] => {\n  return allQuestions.filter(q => q.type === type);\n};\n"], "names": [], "mappings": ";;;;;AAWA,MAAM,eAA2B;IAC/B,8BAA8B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,eAAe;IACjB;IAEA,gCAAgC;IAChC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAK;YAAK;YAAK;YAAM;SAAI;QACnC,SAAS;YAAC;YAAM;YAAM;YAAM;SAAK;QACjC,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QAClC,SAAS;YAAC;YAAK;YAAK;YAAK;SAAI;QAC7B,eAAe;QACf,aAAa;IACf;IAEA,wBAAwB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAK;YAAM;YAAM;SAAK;QAChC,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAM;YAAM;YAAM;SAAK;QACjC,eAAe;QACf,aAAa;IACf;IAEA,iCAAiC;IACjC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAU;YAAa;YAAY;SAAU;QACvD,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,eAAe;IACjB;CACD;AAED,4CAA4C;AAC5C,MAAM,8BAA8B;IAClC,MAAM,sBAAkC,EAAE;IAC1C,IAAI,KAAK;IAET,uCAAuC;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU,CAAC,2BAA2B,EAAE,IAAI,EAAE,sEAAsE,CAAC;YACrH,SAAS;gBAAC;gBAAe;gBAAe;gBAAyB;aAAmB;YACpF,eAAe;QACjB;IACF;IAEA,yCAAyC;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,WAAW;YAAC,IAAI,IAAI;YAAG,IAAI,IAAI;YAAG,IAAI,IAAI;YAAG,IAAI,IAAI;SAAE;QAC7D,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,MAAM,CAAC;gBAAC;aAAI;YACrD,SAAS;gBAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;gBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;gBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;gBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;aAAG;YACjI,eAAe;QACjB;IACF;IAEA,iCAAiC;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,IAAI,IAAI;QACd,MAAM,IAAI,IAAI;QACd,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAChC,SAAS;gBAAC,CAAC,IAAI,CAAC,EAAE,QAAQ;gBAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ;gBAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ;gBAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ;aAAG;YACrG,eAAe;QACjB;IACF;IAEA,0CAA0C;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,QAAQ;YAAC;YAAY;YAAc;YAAa;YAAa;YAAa;YAAU;YAAU;SAAW;QAC/G,MAAM,WAAW;YAAC;YAAc;YAAW;YAAa;YAAc;YAAa;YAAU;YAAU;SAAc;QAErH,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU,CAAC,0CAA0C,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;YACnE,SAAS;gBAAC,QAAQ,CAAC,EAAE;gBAAE;gBAAY;gBAAa;aAAY;YAC5D,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEA,MAAM,eAAe;OAAI;OAAiB;CAA8B;AAEjE,MAAM,kBAAkB,CAAC;IAC9B,8CAA8C;IAC9C,MAAM,gBAAgB,iBAAiB;IACvC,IAAI,iBAAiB,KAAK,gBAAgB,aAAa,MAAM,EAAE;QAC7D,OAAO,YAAY,CAAC,cAAc;IACpC;IAEA,8CAA8C;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAY;YAAY;YAAY;SAAW;QACzD,eAAe;IACjB;AACF;AAEO,MAAM,oBAAoB;IAC/B,OAAO,aAAa,MAAM;AAC5B;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;AAC7C"}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/TestInterface.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChevronLeft, ChevronRight, Send } from 'lucide-react';\nimport LogicalReasoning from './QuestionTypes/LogicalReasoning';\nimport PatternRecognition from './QuestionTypes/PatternRecognition';\nimport MathematicalProblems from './QuestionTypes/MathematicalProblems';\nimport VerbalComprehension from './QuestionTypes/VerbalComprehension';\nimport { getQuestionData } from '@/lib/questions';\n\ninterface TestInterfaceProps {\n  questionNumber: number;\n  totalQuestions: number;\n  selectedAnswer: string | undefined;\n  onAnswerSelect: (answer: string) => void;\n  onNext: () => void;\n  onPrevious: () => void;\n  onSubmit: () => void;\n  canGoNext: boolean;\n  canGoPrevious: boolean;\n  isLastQuestion: boolean;\n}\n\nexport default function TestInterface({\n  questionNumber,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  onSubmit,\n  canGoNext,\n  canGoPrevious,\n  isLastQuestion\n}: TestInterfaceProps) {\n  const [questionData, setQuestionData] = useState<any>(null);\n\n  useEffect(() => {\n    const data = getQuestionData(questionNumber);\n    setQuestionData(data);\n  }, [questionNumber]);\n\n  if (!questionData) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-4\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-8\"></div>\n          <div className=\"space-y-3\">\n            <div className=\"h-4 bg-gray-200 rounded\"></div>\n            <div className=\"h-4 bg-gray-200 rounded\"></div>\n            <div className=\"h-4 bg-gray-200 rounded\"></div>\n            <div className=\"h-4 bg-gray-200 rounded\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const renderQuestionComponent = () => {\n    switch (questionData.type) {\n      case 'logical':\n        return (\n          <LogicalReasoning\n            question={questionData}\n            selectedAnswer={selectedAnswer}\n            onAnswerSelect={onAnswerSelect}\n          />\n        );\n      case 'pattern':\n        return (\n          <PatternRecognition\n            question={questionData}\n            selectedAnswer={selectedAnswer}\n            onAnswerSelect={onAnswerSelect}\n          />\n        );\n      case 'mathematical':\n        return (\n          <MathematicalProblems\n            question={questionData}\n            selectedAnswer={selectedAnswer}\n            onAnswerSelect={onAnswerSelect}\n          />\n        );\n      case 'verbal':\n        return (\n          <VerbalComprehension\n            question={questionData}\n            selectedAnswer={selectedAnswer}\n            onAnswerSelect={onAnswerSelect}\n          />\n        );\n      default:\n        return <div>Unknown question type</div>;\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-8\">\n      {/* Question Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            Question {questionNumber}\n          </h2>\n          <span className=\"text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\">\n            {questionData.type.charAt(0).toUpperCase() + questionData.type.slice(1)} Reasoning\n          </span>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"mb-8\">\n        {renderQuestionComponent()}\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between items-center pt-6 border-t\">\n        <button\n          onClick={onPrevious}\n          disabled={!canGoPrevious}\n          className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${\n            canGoPrevious\n              ? 'text-gray-700 bg-gray-100 hover:bg-gray-200'\n              : 'text-gray-400 bg-gray-50 cursor-not-allowed'\n          }`}\n        >\n          <ChevronLeft className=\"h-4 w-4\" />\n          Previous\n        </button>\n\n        <div className=\"text-sm text-gray-500\">\n          {selectedAnswer ? (\n            <span className=\"text-green-600 font-medium\">Answer selected</span>\n          ) : (\n            <span>Select an answer to continue</span>\n          )}\n        </div>\n\n        {isLastQuestion ? (\n          <button\n            onClick={onSubmit}\n            className=\"flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors\"\n          >\n            <Send className=\"h-4 w-4\" />\n            Submit Test\n          </button>\n        ) : (\n          <button\n            onClick={onNext}\n            disabled={!canGoNext}\n            className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${\n              canGoNext\n                ? 'text-white bg-blue-600 hover:bg-blue-700'\n                : 'text-gray-400 bg-gray-50 cursor-not-allowed'\n            }`}\n          >\n            Next\n            <ChevronRight className=\"h-4 w-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AALA;AAAA;AAAA;AAHA;;;;;;;;;AAuBe,SAAS,cAAc,EACpC,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,MAAM,EACN,UAAU,EACV,QAAQ,EACR,SAAS,EACT,aAAa,EACb,cAAc,EACK;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE;QAC7B,gBAAgB;IAClB,GAAG;QAAC;KAAe;IAEnB,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,MAAM,0BAA0B;QAC9B,OAAQ,aAAa,IAAI;YACvB,KAAK;gBACH,qBACE,8OAAC,uJAAA,CAAA,UAAgB;oBACf,UAAU;oBACV,gBAAgB;oBAChB,gBAAgB;;;;;;YAGtB,KAAK;gBACH,qBACE,8OAAC,yJAAA,CAAA,UAAkB;oBACjB,UAAU;oBACV,gBAAgB;oBAChB,gBAAgB;;;;;;YAGtB,KAAK;gBACH,qBACE,8OAAC,2JAAA,CAAA,UAAoB;oBACnB,UAAU;oBACV,gBAAgB;oBAChB,gBAAgB;;;;;;YAGtB,KAAK;gBACH,qBACE,8OAAC,0JAAA,CAAA,UAAmB;oBAClB,UAAU;oBACV,gBAAgB;oBAChB,gBAAgB;;;;;;YAGtB;gBACE,qBAAO,8OAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCACxC;;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;;gCACb,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC;gCAAG;;;;;;;;;;;;;;;;;;0BAM9E,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,UAAU,CAAC;wBACX,WAAW,CAAC,2EAA2E,EACrF,gBACI,gDACA,+CACJ;;0CAEF,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;kCACZ,+BACC,8OAAC;4BAAK,WAAU;sCAA6B;;;;;iDAE7C,8OAAC;sCAAK;;;;;;;;;;;oBAIT,+BACC,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;6CAI9B,8OAAC;wBACC,SAAS;wBACT,UAAU,CAAC;wBACX,WAAW,CAAC,2EAA2E,EACrF,YACI,6CACA,+CACJ;;4BACH;0CAEC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMpC"}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/Timer.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { Clock, AlertTriangle } from 'lucide-react';\n\ninterface TimerProps {\n  timeRemaining: number;\n  onTimeUp: () => void;\n  onTimeUpdate: (time: number) => void;\n}\n\nexport default function Timer({ timeRemaining, onTimeUp, onTimeUpdate }: TimerProps) {\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      onTimeUp();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      onTimeUpdate(timeRemaining - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeRemaining, onTimeUp, onTimeUpdate]);\n\n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const isLowTime = timeRemaining <= 300; // 5 minutes\n  const isCriticalTime = timeRemaining <= 60; // 1 minute\n\n  return (\n    <div className={`flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-semibold ${\n      isCriticalTime \n        ? 'bg-red-100 text-red-700 border border-red-300' \n        : isLowTime \n        ? 'bg-yellow-100 text-yellow-700 border border-yellow-300'\n        : 'bg-blue-100 text-blue-700 border border-blue-300'\n    }`}>\n      {isCriticalTime ? (\n        <AlertTriangle className=\"h-5 w-5 animate-pulse\" />\n      ) : (\n        <Clock className=\"h-5 w-5\" />\n      )}\n      <span>{formatTime(timeRemaining)}</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAWe,SAAS,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAc;IACjF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,GAAG;YACtB;YACA;QACF;QAEA,MAAM,QAAQ,YAAY;YACxB,aAAa,gBAAgB;QAC/B,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe;QAAU;KAAa;IAE1C,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACjG;IAEA,MAAM,YAAY,iBAAiB,KAAK,YAAY;IACpD,MAAM,iBAAiB,iBAAiB,IAAI,WAAW;IAEvD,qBACE,8OAAC;QAAI,WAAW,CAAC,6EAA6E,EAC5F,iBACI,kDACA,YACA,2DACA,oDACJ;;YACC,+BACC,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;qCAEzB,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;0BAEnB,8OAAC;0BAAM,WAAW;;;;;;;;;;;;AAGxB"}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/ProgressBar.tsx"], "sourcesContent": ["interface ProgressBarProps {\n  current: number;\n  total: number;\n}\n\nexport default function ProgressBar({ current, total }: ProgressBarProps) {\n  const percentage = (current / total) * 100;\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\n        <span>Question {current} of {total}</span>\n        <span>{Math.round(percentage)}% Complete</span>\n      </div>\n      <div className=\"w-full bg-gray-200 rounded-full h-3\">\n        <div \n          className=\"bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out\"\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAKe,SAAS,YAAY,EAAE,OAAO,EAAE,KAAK,EAAoB;IACtE,MAAM,aAAa,AAAC,UAAU,QAAS;IAEvC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAK;4BAAU;4BAAQ;4BAAK;;;;;;;kCAC7B,8OAAC;;4BAAM,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAEhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C"}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/app/test/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport TestInterface from '@/components/TestInterface';\nimport Timer from '@/components/Timer';\nimport ProgressBar from '@/components/ProgressBar';\nimport { Brain, Clock, AlertCircle } from 'lucide-react';\n\nexport default function TestPage() {\n  const [testStarted, setTestStarted] = useState(false);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState<Record<number, string>>({});\n  const [timeRemaining, setTimeRemaining] = useState(45 * 60); // 45 minutes\n  const router = useRouter();\n\n  const totalQuestions = 40;\n  const testDuration = 45 * 60; // 45 minutes in seconds\n\n  const handleStartTest = () => {\n    setTestStarted(true);\n  };\n\n  const handleAnswerSelect = (questionId: number, answer: string) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestion < totalQuestions - 1) {\n      setCurrentQuestion(prev => prev + 1);\n    }\n  };\n\n  const handlePreviousQuestion = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(prev => prev - 1);\n    }\n  };\n\n  const handleSubmitTest = () => {\n    // Calculate score and redirect to results\n    const score = calculateScore(answers);\n    const testId = Date.now().toString();\n    \n    // Store results in localStorage for now (later we'll use Supabase)\n    localStorage.setItem(`test_${testId}`, JSON.stringify({\n      answers,\n      score,\n      completedAt: new Date().toISOString(),\n      timeSpent: testDuration - timeRemaining\n    }));\n    \n    router.push(`/results/${testId}`);\n  };\n\n  const calculateScore = (answers: Record<number, string>) => {\n    // Simplified scoring logic - in a real app this would be more sophisticated\n    const correctAnswers = Object.keys(answers).length;\n    const percentage = (correctAnswers / totalQuestions) * 100;\n    return Math.round(85 + (percentage - 50) * 0.6); // Basic IQ calculation\n  };\n\n  const handleTimeUp = () => {\n    handleSubmitTest();\n  };\n\n  if (!testStarted) {\n    return (\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"text-center mb-8\">\n            <Brain className=\"h-16 w-16 text-blue-600 mx-auto mb-4\" />\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">IQ Test Instructions</h1>\n            <p className=\"text-lg text-gray-600\">\n              You are about to take a comprehensive IQ assessment\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-8 mb-8\">\n            <div className=\"bg-blue-50 p-6 rounded-lg\">\n              <Clock className=\"h-8 w-8 text-blue-600 mb-3\" />\n              <h3 className=\"font-semibold mb-2\">Time Limit</h3>\n              <p className=\"text-gray-700\">45 minutes to complete 40 questions</p>\n            </div>\n            <div className=\"bg-green-50 p-6 rounded-lg\">\n              <AlertCircle className=\"h-8 w-8 text-green-600 mb-3\" />\n              <h3 className=\"font-semibold mb-2\">Instructions</h3>\n              <p className=\"text-gray-700\">Choose the best answer for each question</p>\n            </div>\n          </div>\n\n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8\">\n            <h3 className=\"font-semibold text-yellow-800 mb-2\">Important Notes:</h3>\n            <ul className=\"list-disc list-inside text-yellow-700 space-y-1\">\n              <li>You cannot pause the test once started</li>\n              <li>Questions cover logical reasoning, math, verbal, and pattern recognition</li>\n              <li>Work quickly but carefully</li>\n              <li>You can review and change answers before submitting</li>\n            </ul>\n          </div>\n\n          <div className=\"text-center\">\n            <button\n              onClick={handleStartTest}\n              className=\"bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto\"\n            >\n              <Brain className=\"h-5 w-5\" />\n              Start Test\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      {/* Test Header */}\n      <div className=\"bg-white rounded-lg shadow-lg p-6 mb-6\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">IQ Test</h1>\n          <Timer \n            timeRemaining={timeRemaining} \n            onTimeUp={handleTimeUp}\n            onTimeUpdate={setTimeRemaining}\n          />\n        </div>\n        <ProgressBar \n          current={currentQuestion + 1} \n          total={totalQuestions} \n        />\n      </div>\n\n      {/* Test Interface */}\n      <TestInterface\n        questionNumber={currentQuestion + 1}\n        totalQuestions={totalQuestions}\n        selectedAnswer={answers[currentQuestion]}\n        onAnswerSelect={(answer) => handleAnswerSelect(currentQuestion, answer)}\n        onNext={handleNextQuestion}\n        onPrevious={handlePreviousQuestion}\n        onSubmit={handleSubmitTest}\n        canGoNext={currentQuestion < totalQuestions - 1}\n        canGoPrevious={currentQuestion > 0}\n        isLastQuestion={currentQuestion === totalQuestions - 1}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,aAAa;IAC1E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB;IACvB,MAAM,eAAe,KAAK,IAAI,wBAAwB;IAEtD,MAAM,kBAAkB;QACtB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,IAAI,kBAAkB,iBAAiB,GAAG;YACxC,mBAAmB,CAAA,OAAQ,OAAO;QACpC;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,kBAAkB,GAAG;YACvB,mBAAmB,CAAA,OAAQ,OAAO;QACpC;IACF;IAEA,MAAM,mBAAmB;QACvB,0CAA0C;QAC1C,MAAM,QAAQ,eAAe;QAC7B,MAAM,SAAS,KAAK,GAAG,GAAG,QAAQ;QAElC,mEAAmE;QACnE,aAAa,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;YACpD;YACA;YACA,aAAa,IAAI,OAAO,WAAW;YACnC,WAAW,eAAe;QAC5B;QAEA,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ;IAClC;IAEA,MAAM,iBAAiB,CAAC;QACtB,4EAA4E;QAC5E,MAAM,iBAAiB,OAAO,IAAI,CAAC,SAAS,MAAM;QAClD,MAAM,aAAa,AAAC,iBAAiB,iBAAkB;QACvD,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,MAAM,uBAAuB;IAC1E;IAEA,MAAM,eAAe;QACnB;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAIjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAIR,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;IAOzC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC,2HAAA,CAAA,UAAK;gCACJ,eAAe;gCACf,UAAU;gCACV,cAAc;;;;;;;;;;;;kCAGlB,8OAAC,iIAAA,CAAA,UAAW;wBACV,SAAS,kBAAkB;wBAC3B,OAAO;;;;;;;;;;;;0BAKX,8OAAC,mIAAA,CAAA,UAAa;gBACZ,gBAAgB,kBAAkB;gBAClC,gBAAgB;gBAChB,gBAAgB,OAAO,CAAC,gBAAgB;gBACxC,gBAAgB,CAAC,SAAW,mBAAmB,iBAAiB;gBAChE,QAAQ;gBACR,YAAY;gBACZ,UAAU;gBACV,WAAW,kBAAkB,iBAAiB;gBAC9C,eAAe,kBAAkB;gBACjC,gBAAgB,oBAAoB,iBAAiB;;;;;;;;;;;;AAI7D"}}, {"offset": {"line": 1487, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "file": "chevron-left.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/chevron-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm15 18-6-6 6-6', key: '1wnfg3' }]];\n\n/**\n * @component @name ChevronLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtNi02IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronLeft = createLucideIcon('chevron-left', __iconNode);\n\nexport default ChevronLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "file": "chevron-right.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "file": "send.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/send.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n];\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('send', __iconNode);\n\nexport default Send;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1622, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1723, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}