'use client';

import { useEffect, useState } from 'react';

interface ScoreDisplayProps {
  score: number;
}

export default function ScoreDisplay({ score }: ScoreDisplayProps) {
  const [animatedScore, setAnimatedScore] = useState(0);

  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const increment = score / steps;
    let current = 0;
    
    const timer = setInterval(() => {
      current += increment;
      if (current >= score) {
        setAnimatedScore(score);
        clearInterval(timer);
      } else {
        setAnimatedScore(Math.floor(current));
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [score]);

  const getScoreColor = (score: number) => {
    if (score >= 130) return 'text-purple-600';
    if (score >= 120) return 'text-blue-600';
    if (score >= 110) return 'text-green-600';
    if (score >= 90) return 'text-gray-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 130) return 'bg-purple-100';
    if (score >= 120) return 'bg-blue-100';
    if (score >= 110) return 'bg-green-100';
    if (score >= 90) return 'bg-gray-100';
    if (score >= 80) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="text-center">
      <div className={`inline-flex items-center justify-center w-32 h-32 rounded-full ${getScoreBackground(score)} mb-4`}>
        <span className={`text-4xl font-bold ${getScoreColor(score)}`}>
          {animatedScore}
        </span>
      </div>
      
      {/* Score Bar */}
      <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
        <div 
          className={`h-4 rounded-full transition-all duration-2000 ease-out ${
            score >= 130 ? 'bg-purple-600' :
            score >= 120 ? 'bg-blue-600' :
            score >= 110 ? 'bg-green-600' :
            score >= 90 ? 'bg-gray-600' :
            score >= 80 ? 'bg-yellow-600' : 'bg-red-600'
          }`}
          style={{ width: `${Math.min((score / 160) * 100, 100)}%` }}
        />
      </div>
      
      <div className="text-sm text-gray-600">
        <div className="flex justify-between">
          <span>70</span>
          <span>100</span>
          <span>130</span>
          <span>160</span>
        </div>
      </div>
    </div>
  );
}
