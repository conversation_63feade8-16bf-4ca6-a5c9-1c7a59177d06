{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Brain, Home, BarChart3, Settings, Play } from 'lucide-react';\n\nexport default function Navigation() {\n  const pathname = usePathname();\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Home },\n    { href: '/test', label: 'Take Test', icon: Brain },\n    { href: '/practice', label: 'Practice', icon: Play },\n    { href: '/dashboard', label: 'Dashboard', icon: BarChart3 },\n    { href: '/admin', label: 'Admin', icon: Settings },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Brain className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">IQ Test Pro</span>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex space-x-8\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    isActive\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button className=\"text-gray-700 hover:text-blue-600\">\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,mMAAA,CAAA,OAAI;QAAC;QACvC;YAAE,MAAM;YAAS,OAAO;YAAa,MAAM,oMAAA,CAAA,QAAK;QAAC;QACjD;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;QACnD;YAAE,MAAM;YAAc,OAAO;YAAa,MAAM,kNAAA,CAAA,YAAS;QAAC;QAC1D;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAClD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAkC;;;;;;;;;;;;kCAIpD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,uFAAuF,EACjG,WACI,6BACA,sDACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,KAAK,KAAK;;;;;;;+BATZ,KAAK,IAAI;;;;;wBAYpB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF"}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}