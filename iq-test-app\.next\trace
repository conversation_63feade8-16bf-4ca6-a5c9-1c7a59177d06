[{"name": "hot-reloader", "duration": 61, "timestamp": 891857379144, "id": 3, "tags": {"version": "15.1.8"}, "startTime": 1748077895840, "traceId": "4a51089828d30eee"}, {"name": "setup-dev-bundler", "duration": 712191, "timestamp": 891857150373, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748077895611, "traceId": "4a51089828d30eee"}, {"name": "run-instrumentation-hook", "duration": 27, "timestamp": 891857936905, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748077896397, "traceId": "4a51089828d30eee"}, {"name": "start-dev-server", "duration": 1351211, "timestamp": 891856601186, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "6175571968", "memory.totalMem": "16907886592", "memory.heapSizeLimit": "8503951360", "memory.rss": "175845376", "memory.heapTotal": "97058816", "memory.heapUsed": "73056328"}, "startTime": 1748077895062, "traceId": "4a51089828d30eee"}, {"name": "compile-path", "duration": 3436477, "timestamp": 891879884125, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748077918344, "traceId": "4a51089828d30eee"}, {"name": "ensure-page", "duration": 3437880, "timestamp": 891879883493, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748077918344, "traceId": "4a51089828d30eee"}]