interface Question {
  id: number;
  type: 'logical' | 'pattern' | 'mathematical' | 'verbal';
  question: string;
  options: string[];
  correctAnswer: string;
  explanation?: string;
  pattern?: string[];
  passage?: string;
}

const questionBank: Question[] = [
  // Logical Reasoning Questions
  {
    id: 1,
    type: 'logical',
    question: 'All roses are flowers. Some flowers are red. Therefore:',
    options: [
      'All roses are red',
      'Some roses may be red',
      'No roses are red',
      'All red things are roses'
    ],
    correctAnswer: 'B',
    explanation: 'The conclusion follows logically from the premises.'
  },
  {
    id: 2,
    type: 'logical',
    question: 'If all cats are mammals and all mammals are animals, then:',
    options: [
      'All animals are cats',
      'All cats are animals',
      'Some animals are not mammals',
      'No cats are animals'
    ],
    correctAnswer: 'B'
  },

  // Pattern Recognition Questions
  {
    id: 3,
    type: 'pattern',
    question: 'What comes next in this sequence?',
    pattern: ['2', '4', '8', '16', '?'],
    options: ['24', '32', '20', '18'],
    correctAnswer: 'B',
    explanation: 'Each number is doubled: 2×2=4, 4×2=8, 8×2=16, 16×2=32'
  },
  {
    id: 4,
    type: 'pattern',
    question: 'Complete the pattern:',
    pattern: ['A', 'C', 'E', 'G', '?'],
    options: ['H', 'I', 'J', 'K'],
    correctAnswer: 'B',
    explanation: 'Skip one letter each time: A(skip B)C(skip D)E(skip F)G(skip H)I'
  },

  // Mathematical Problems
  {
    id: 5,
    type: 'mathematical',
    question: 'If x + 5 = 12, what is the value of 2x?',
    options: ['7', '14', '10', '12'],
    correctAnswer: 'B',
    explanation: 'x = 7, so 2x = 14'
  },
  {
    id: 6,
    type: 'mathematical',
    question: 'What is 15% of 200?',
    options: ['25', '30', '35', '40'],
    correctAnswer: 'B',
    explanation: '15% of 200 = 0.15 × 200 = 30'
  },

  // Verbal Comprehension Questions
  {
    id: 7,
    type: 'verbal',
    question: 'Which word is most similar in meaning to "abundant"?',
    options: ['Scarce', 'Plentiful', 'Moderate', 'Limited'],
    correctAnswer: 'B',
    explanation: 'Abundant means existing in large quantities; plentiful.'
  },
  {
    id: 8,
    type: 'verbal',
    passage: 'The rapid advancement of technology has transformed how we communicate, work, and live. While these changes bring many benefits, they also present new challenges that society must address.',
    question: 'Based on the passage, technological advancement:',
    options: [
      'Only brings benefits',
      'Only creates problems',
      'Has both positive and negative effects',
      'Should be avoided'
    ],
    correctAnswer: 'C'
  }
];

// Generate more questions to reach 40 total
const generateAdditionalQuestions = (): Question[] => {
  const additionalQuestions: Question[] = [];
  let id = 9;

  // Add more logical reasoning questions
  for (let i = 0; i < 8; i++) {
    additionalQuestions.push({
      id: id++,
      type: 'logical',
      question: `Logical reasoning question ${i + 3}: If A implies B, and B implies C, what can we conclude about A and C?`,
      options: ['A implies C', 'C implies A', 'A and C are unrelated', 'Cannot determine'],
      correctAnswer: 'A'
    });
  }

  // Add more pattern recognition questions
  for (let i = 0; i < 8; i++) {
    const sequence = [1 + i * 2, 3 + i * 2, 5 + i * 2, 7 + i * 2];
    additionalQuestions.push({
      id: id++,
      type: 'pattern',
      question: 'What is the next number in the sequence?',
      pattern: sequence.map(n => n.toString()).concat(['?']),
      options: [(sequence[3] + 2).toString(), (sequence[3] + 3).toString(), (sequence[3] + 1).toString(), (sequence[3] + 4).toString()],
      correctAnswer: 'A'
    });
  }

  // Add more mathematical problems
  for (let i = 0; i < 8; i++) {
    const a = 5 + i;
    const b = 3 + i;
    additionalQuestions.push({
      id: id++,
      type: 'mathematical',
      question: `What is ${a} × ${b}?`,
      options: [(a * b).toString(), (a * b + 1).toString(), (a * b - 1).toString(), (a * b + 2).toString()],
      correctAnswer: 'A'
    });
  }

  // Add more verbal comprehension questions
  for (let i = 0; i < 8; i++) {
    const words = ['Eloquent', 'Meticulous', 'Pragmatic', 'Tenacious', 'Versatile', 'Astute', 'Candid', 'Diligent'];
    const synonyms = ['Articulate', 'Careful', 'Practical', 'Persistent', 'Adaptable', 'Clever', 'Honest', 'Hardworking'];
    
    additionalQuestions.push({
      id: id++,
      type: 'verbal',
      question: `Which word is most similar in meaning to "${words[i]}"?`,
      options: [synonyms[i], 'Opposite', 'Unrelated', 'Different'],
      correctAnswer: 'A'
    });
  }

  return additionalQuestions;
};

const allQuestions = [...questionBank, ...generateAdditionalQuestions()];

export const getQuestionData = (questionNumber: number): Question => {
  // Return question based on number (1-indexed)
  const questionIndex = questionNumber - 1;
  if (questionIndex >= 0 && questionIndex < allQuestions.length) {
    return allQuestions[questionIndex];
  }
  
  // Fallback question if index is out of bounds
  return {
    id: questionNumber,
    type: 'logical',
    question: 'Sample question for testing purposes.',
    options: ['Option A', 'Option B', 'Option C', 'Option D'],
    correctAnswer: 'A'
  };
};

export const getTotalQuestions = (): number => {
  return allQuestions.length;
};

export const getQuestionsByType = (type: Question['type']): Question[] => {
  return allQuestions.filter(q => q.type === type);
};
