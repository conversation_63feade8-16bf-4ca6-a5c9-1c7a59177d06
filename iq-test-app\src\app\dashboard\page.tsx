'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { BarChart3, Brain, Clock, TrendingUp, Calendar, Award } from 'lucide-react';

interface TestHistory {
  id: string;
  score: number;
  completedAt: string;
  timeSpent: number;
  questionsAnswered: number;
}

export default function DashboardPage() {
  const [testHistory, setTestHistory] = useState<TestHistory[]>([]);
  const [stats, setStats] = useState({
    totalTests: 0,
    averageScore: 0,
    bestScore: 0,
    totalTimeSpent: 0
  });

  useEffect(() => {
    // Load test history from localStorage
    const history: TestHistory[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('test_')) {
        try {
          const testData = JSON.parse(localStorage.getItem(key) || '');
          history.push({
            id: key.replace('test_', ''),
            score: testData.score,
            completedAt: testData.completedAt,
            timeSpent: testData.timeSpent,
            questionsAnswered: Object.keys(testData.answers).length
          });
        } catch (error) {
          console.error('Error parsing test data:', error);
        }
      }
    }

    // Sort by completion date (newest first)
    history.sort((a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime());
    setTestHistory(history);

    // Calculate stats
    if (history.length > 0) {
      const totalScore = history.reduce((sum, test) => sum + test.score, 0);
      const totalTime = history.reduce((sum, test) => sum + test.timeSpent, 0);
      
      setStats({
        totalTests: history.length,
        averageScore: Math.round(totalScore / history.length),
        bestScore: Math.max(...history.map(test => test.score)),
        totalTimeSpent: totalTime
      });
    }
  }, []);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m ${seconds % 60}s`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 130) return 'text-purple-600';
    if (score >= 120) return 'text-blue-600';
    if (score >= 110) return 'text-green-600';
    if (score >= 90) return 'text-gray-600';
    return 'text-yellow-600';
  };

  if (testHistory.length === 0) {
    return (
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-white rounded-lg shadow-lg p-12">
          <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Your Dashboard</h1>
          <p className="text-xl text-gray-600 mb-8">
            No test results yet. Take your first IQ test to see your progress here.
          </p>
          <Link
            href="/test"
            className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
          >
            <Brain className="h-5 w-5" />
            Take Your First Test
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="flex items-center gap-4 mb-4">
          <BarChart3 className="h-12 w-12 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Your Dashboard</h1>
            <p className="text-gray-600">Track your IQ test performance and progress</p>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-2">
            <Brain className="h-8 w-8 text-blue-600" />
            <span className="text-sm font-medium text-gray-600">Tests Taken</span>
          </div>
          <div className="text-3xl font-bold text-gray-900">{stats.totalTests}</div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-2">
            <TrendingUp className="h-8 w-8 text-green-600" />
            <span className="text-sm font-medium text-gray-600">Average Score</span>
          </div>
          <div className={`text-3xl font-bold ${getScoreColor(stats.averageScore)}`}>
            {stats.averageScore}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-2">
            <Award className="h-8 w-8 text-purple-600" />
            <span className="text-sm font-medium text-gray-600">Best Score</span>
          </div>
          <div className={`text-3xl font-bold ${getScoreColor(stats.bestScore)}`}>
            {stats.bestScore}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center gap-3 mb-2">
            <Clock className="h-8 w-8 text-orange-600" />
            <span className="text-sm font-medium text-gray-600">Total Time</span>
          </div>
          <div className="text-3xl font-bold text-gray-900">
            {formatTime(stats.totalTimeSpent)}
          </div>
        </div>
      </div>

      {/* Test History */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Test History</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Score</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Questions</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Time</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {testHistory.map((test) => (
                <tr key={test.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {new Date(test.completedAt).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`text-xl font-bold ${getScoreColor(test.score)}`}>
                      {test.score}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-gray-700">{test.questionsAnswered}/40</span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="text-gray-700">{formatTime(test.timeSpent)}</span>
                  </td>
                  <td className="py-4 px-4">
                    <Link
                      href={`/results/${test.id}`}
                      className="text-blue-600 hover:text-blue-800 font-medium"
                    >
                      View Details
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Actions */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Ready for Another Challenge?</h2>
        <p className="text-lg mb-6 opacity-90">
          Continue improving your cognitive abilities with another test
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/test"
            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center gap-2"
          >
            <Brain className="h-5 w-5" />
            Take New Test
          </Link>
          <Link
            href="/practice"
            className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:bg-opacity-10 transition-colors inline-flex items-center justify-center gap-2"
          >
            Practice Mode
          </Link>
        </div>
      </div>
    </div>
  );
}
