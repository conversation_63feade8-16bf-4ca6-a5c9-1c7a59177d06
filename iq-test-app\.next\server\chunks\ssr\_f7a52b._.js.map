{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/lib/questions.ts"], "sourcesContent": ["interface Question {\n  id: number;\n  type: 'logical' | 'pattern' | 'mathematical' | 'verbal';\n  question: string;\n  options: string[];\n  correctAnswer: string;\n  explanation?: string;\n  pattern?: string[];\n  passage?: string;\n}\n\nconst questionBank: Question[] = [\n  // Logical Reasoning Questions\n  {\n    id: 1,\n    type: 'logical',\n    question: 'All roses are flowers. Some flowers are red. Therefore:',\n    options: [\n      'All roses are red',\n      'Some roses may be red',\n      'No roses are red',\n      'All red things are roses'\n    ],\n    correctAnswer: 'B',\n    explanation: 'The conclusion follows logically from the premises.'\n  },\n  {\n    id: 2,\n    type: 'logical',\n    question: 'If all cats are mammals and all mammals are animals, then:',\n    options: [\n      'All animals are cats',\n      'All cats are animals',\n      'Some animals are not mammals',\n      'No cats are animals'\n    ],\n    correctAnswer: 'B'\n  },\n\n  // Pattern Recognition Questions\n  {\n    id: 3,\n    type: 'pattern',\n    question: 'What comes next in this sequence?',\n    pattern: ['2', '4', '8', '16', '?'],\n    options: ['24', '32', '20', '18'],\n    correctAnswer: 'B',\n    explanation: 'Each number is doubled: 2×2=4, 4×2=8, 8×2=16, 16×2=32'\n  },\n  {\n    id: 4,\n    type: 'pattern',\n    question: 'Complete the pattern:',\n    pattern: ['A', 'C', 'E', 'G', '?'],\n    options: ['H', 'I', 'J', 'K'],\n    correctAnswer: 'B',\n    explanation: 'Skip one letter each time: A(skip B)C(skip D)E(skip F)G(skip H)I'\n  },\n\n  // Mathematical Problems\n  {\n    id: 5,\n    type: 'mathematical',\n    question: 'If x + 5 = 12, what is the value of 2x?',\n    options: ['7', '14', '10', '12'],\n    correctAnswer: 'B',\n    explanation: 'x = 7, so 2x = 14'\n  },\n  {\n    id: 6,\n    type: 'mathematical',\n    question: 'What is 15% of 200?',\n    options: ['25', '30', '35', '40'],\n    correctAnswer: 'B',\n    explanation: '15% of 200 = 0.15 × 200 = 30'\n  },\n\n  // Verbal Comprehension Questions\n  {\n    id: 7,\n    type: 'verbal',\n    question: 'Which word is most similar in meaning to \"abundant\"?',\n    options: ['Scarce', 'Plentiful', 'Moderate', 'Limited'],\n    correctAnswer: 'B',\n    explanation: 'Abundant means existing in large quantities; plentiful.'\n  },\n  {\n    id: 8,\n    type: 'verbal',\n    passage: 'The rapid advancement of technology has transformed how we communicate, work, and live. While these changes bring many benefits, they also present new challenges that society must address.',\n    question: 'Based on the passage, technological advancement:',\n    options: [\n      'Only brings benefits',\n      'Only creates problems',\n      'Has both positive and negative effects',\n      'Should be avoided'\n    ],\n    correctAnswer: 'C'\n  }\n];\n\n// Generate more questions to reach 40 total\nconst generateAdditionalQuestions = (): Question[] => {\n  const additionalQuestions: Question[] = [];\n  let id = 9;\n\n  // Add more logical reasoning questions\n  for (let i = 0; i < 8; i++) {\n    additionalQuestions.push({\n      id: id++,\n      type: 'logical',\n      question: `Logical reasoning question ${i + 3}: If A implies B, and B implies C, what can we conclude about A and C?`,\n      options: ['A implies C', 'C implies A', 'A and C are unrelated', 'Cannot determine'],\n      correctAnswer: 'A'\n    });\n  }\n\n  // Add more pattern recognition questions\n  for (let i = 0; i < 8; i++) {\n    const sequence = [1 + i * 2, 3 + i * 2, 5 + i * 2, 7 + i * 2];\n    additionalQuestions.push({\n      id: id++,\n      type: 'pattern',\n      question: 'What is the next number in the sequence?',\n      pattern: sequence.map(n => n.toString()).concat(['?']),\n      options: [(sequence[3] + 2).toString(), (sequence[3] + 3).toString(), (sequence[3] + 1).toString(), (sequence[3] + 4).toString()],\n      correctAnswer: 'A'\n    });\n  }\n\n  // Add more mathematical problems\n  for (let i = 0; i < 8; i++) {\n    const a = 5 + i;\n    const b = 3 + i;\n    additionalQuestions.push({\n      id: id++,\n      type: 'mathematical',\n      question: `What is ${a} × ${b}?`,\n      options: [(a * b).toString(), (a * b + 1).toString(), (a * b - 1).toString(), (a * b + 2).toString()],\n      correctAnswer: 'A'\n    });\n  }\n\n  // Add more verbal comprehension questions\n  for (let i = 0; i < 8; i++) {\n    const words = ['Eloquent', 'Meticulous', 'Pragmatic', 'Tenacious', 'Versatile', 'Astute', 'Candid', 'Diligent'];\n    const synonyms = ['Articulate', 'Careful', 'Practical', 'Persistent', 'Adaptable', 'Clever', 'Honest', 'Hardworking'];\n    \n    additionalQuestions.push({\n      id: id++,\n      type: 'verbal',\n      question: `Which word is most similar in meaning to \"${words[i]}\"?`,\n      options: [synonyms[i], 'Opposite', 'Unrelated', 'Different'],\n      correctAnswer: 'A'\n    });\n  }\n\n  return additionalQuestions;\n};\n\nconst allQuestions = [...questionBank, ...generateAdditionalQuestions()];\n\nexport const getQuestionData = (questionNumber: number): Question => {\n  // Return question based on number (1-indexed)\n  const questionIndex = questionNumber - 1;\n  if (questionIndex >= 0 && questionIndex < allQuestions.length) {\n    return allQuestions[questionIndex];\n  }\n  \n  // Fallback question if index is out of bounds\n  return {\n    id: questionNumber,\n    type: 'logical',\n    question: 'Sample question for testing purposes.',\n    options: ['Option A', 'Option B', 'Option C', 'Option D'],\n    correctAnswer: 'A'\n  };\n};\n\nexport const getTotalQuestions = (): number => {\n  return allQuestions.length;\n};\n\nexport const getQuestionsByType = (type: Question['type']): Question[] => {\n  return allQuestions.filter(q => q.type === type);\n};\n"], "names": [], "mappings": ";;;;;AAWA,MAAM,eAA2B;IAC/B,8BAA8B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,eAAe;IACjB;IAEA,gCAAgC;IAChC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAK;YAAK;YAAK;YAAM;SAAI;QACnC,SAAS;YAAC;YAAM;YAAM;YAAM;SAAK;QACjC,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QAClC,SAAS;YAAC;YAAK;YAAK;YAAK;SAAI;QAC7B,eAAe;QACf,aAAa;IACf;IAEA,wBAAwB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAK;YAAM;YAAM;SAAK;QAChC,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAM;YAAM;YAAM;SAAK;QACjC,eAAe;QACf,aAAa;IACf;IAEA,iCAAiC;IACjC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAU;YAAa;YAAY;SAAU;QACvD,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,eAAe;IACjB;CACD;AAED,4CAA4C;AAC5C,MAAM,8BAA8B;IAClC,MAAM,sBAAkC,EAAE;IAC1C,IAAI,KAAK;IAET,uCAAuC;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU,CAAC,2BAA2B,EAAE,IAAI,EAAE,sEAAsE,CAAC;YACrH,SAAS;gBAAC;gBAAe;gBAAe;gBAAyB;aAAmB;YACpF,eAAe;QACjB;IACF;IAEA,yCAAyC;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,WAAW;YAAC,IAAI,IAAI;YAAG,IAAI,IAAI;YAAG,IAAI,IAAI;YAAG,IAAI,IAAI;SAAE;QAC7D,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU;YACV,SAAS,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,MAAM,CAAC;gBAAC;aAAI;YACrD,SAAS;gBAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;gBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;gBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;gBAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ;aAAG;YACjI,eAAe;QACjB;IACF;IAEA,iCAAiC;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,IAAI,IAAI;QACd,MAAM,IAAI,IAAI;QACd,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAChC,SAAS;gBAAC,CAAC,IAAI,CAAC,EAAE,QAAQ;gBAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ;gBAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ;gBAAI,CAAC,IAAI,IAAI,CAAC,EAAE,QAAQ;aAAG;YACrG,eAAe;QACjB;IACF;IAEA,0CAA0C;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,QAAQ;YAAC;YAAY;YAAc;YAAa;YAAa;YAAa;YAAU;YAAU;SAAW;QAC/G,MAAM,WAAW;YAAC;YAAc;YAAW;YAAa;YAAc;YAAa;YAAU;YAAU;SAAc;QAErH,oBAAoB,IAAI,CAAC;YACvB,IAAI;YACJ,MAAM;YACN,UAAU,CAAC,0CAA0C,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;YACnE,SAAS;gBAAC,QAAQ,CAAC,EAAE;gBAAE;gBAAY;gBAAa;aAAY;YAC5D,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEA,MAAM,eAAe;OAAI;OAAiB;CAA8B;AAEjE,MAAM,kBAAkB,CAAC;IAC9B,8CAA8C;IAC9C,MAAM,gBAAgB,iBAAiB;IACvC,IAAI,iBAAiB,KAAK,gBAAgB,aAAa,MAAM,EAAE;QAC7D,OAAO,YAAY,CAAC,cAAc;IACpC;IAEA,8CAA8C;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;YAAC;YAAY;YAAY;YAAY;SAAW;QACzD,eAAe;IACjB;AACF;AAEO,MAAM,oBAAoB;IAC/B,OAAO,aAAa,MAAM;AAC5B;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;AAC7C"}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/src/app/practice/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Brain, Play, BookOpen, Target, Clock } from 'lucide-react';\nimport { getQuestionsByType } from '@/lib/questions';\n\nexport default function PracticePage() {\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);\n\n  const categories = [\n    {\n      id: 'logical',\n      name: 'Logical Reasoning',\n      description: 'Practice logical deduction and reasoning problems',\n      icon: Brain,\n      color: 'blue',\n      questionCount: getQuestionsByType('logical').length\n    },\n    {\n      id: 'pattern',\n      name: 'Pattern Recognition',\n      description: 'Identify patterns and sequences in visual and numerical data',\n      icon: Target,\n      color: 'green',\n      questionCount: getQuestionsByType('pattern').length\n    },\n    {\n      id: 'mathematical',\n      name: 'Mathematical Problems',\n      description: 'Solve numerical reasoning and mathematical concepts',\n      icon: BookOpen,\n      color: 'purple',\n      questionCount: getQuestionsByType('mathematical').length\n    },\n    {\n      id: 'verbal',\n      name: 'Verbal Comprehension',\n      description: 'Test vocabulary, reading comprehension, and verbal reasoning',\n      icon: Clock,\n      color: 'orange',\n      questionCount: getQuestionsByType('verbal').length\n    }\n  ];\n\n  const getColorClasses = (color: string) => {\n    const colors = {\n      blue: 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100',\n      green: 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100',\n      purple: 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100',\n      orange: 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'\n    };\n    return colors[color as keyof typeof colors] || colors.blue;\n  };\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      {/* Header */}\n      <div className=\"text-center mb-12\">\n        <div className=\"flex justify-center mb-6\">\n          <Play className=\"h-16 w-16 text-blue-600\" />\n        </div>\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">Practice Mode</h1>\n        <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n          Sharpen your cognitive skills with practice questions from each category. \n          No time limits, with immediate feedback and explanations.\n        </p>\n      </div>\n\n      {/* Practice Categories */}\n      <div className=\"grid md:grid-cols-2 gap-6 mb-12\">\n        {categories.map((category) => {\n          const Icon = category.icon;\n          return (\n            <div\n              key={category.id}\n              className={`p-8 rounded-lg border-2 transition-all duration-200 cursor-pointer ${getColorClasses(category.color)}`}\n              onClick={() => setSelectedCategory(category.id)}\n            >\n              <div className=\"flex items-start gap-4\">\n                <Icon className=\"h-12 w-12 flex-shrink-0\" />\n                <div className=\"flex-1\">\n                  <h3 className=\"text-xl font-semibold mb-2\">{category.name}</h3>\n                  <p className=\"text-sm opacity-80 mb-4\">{category.description}</p>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm font-medium\">\n                      {category.questionCount} practice questions\n                    </span>\n                    <button className=\"bg-white bg-opacity-50 px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-75 transition-colors\">\n                      Start Practice\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Features */}\n      <div className=\"bg-white rounded-lg shadow-lg p-8 mb-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Practice Mode Features</h2>\n        <div className=\"grid md:grid-cols-3 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Clock className=\"h-8 w-8 text-blue-600\" />\n            </div>\n            <h3 className=\"font-semibold mb-2\">No Time Pressure</h3>\n            <p className=\"text-sm text-gray-600\">Take your time to think through each question carefully</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <BookOpen className=\"h-8 w-8 text-green-600\" />\n            </div>\n            <h3 className=\"font-semibold mb-2\">Instant Feedback</h3>\n            <p className=\"text-sm text-gray-600\">Get immediate explanations for correct and incorrect answers</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Target className=\"h-8 w-8 text-purple-600\" />\n            </div>\n            <h3 className=\"font-semibold mb-2\">Targeted Practice</h3>\n            <p className=\"text-sm text-gray-600\">Focus on specific question types to improve weak areas</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Call to Action */}\n      <div className=\"text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8\">\n        <h2 className=\"text-2xl font-bold mb-4\">Ready for the Real Test?</h2>\n        <p className=\"text-lg mb-6 opacity-90\">\n          Once you've practiced enough, take the full timed IQ assessment\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link\n            href=\"/test\"\n            className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center gap-2\"\n          >\n            <Brain className=\"h-5 w-5\" />\n            Take Full Test\n          </Link>\n          <Link\n            href=\"/\"\n            className=\"border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:bg-opacity-10 transition-colors inline-flex items-center justify-center gap-2\"\n          >\n            Learn More\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AADA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;QACrD;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;QACrD;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,MAAM;QAC1D;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,MAAM;QACpD;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAOzD,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,OAAO,SAAS,IAAI;oBAC1B,qBACE,8OAAC;wBAEC,WAAW,CAAC,mEAAmE,EAAE,gBAAgB,SAAS,KAAK,GAAG;wBAClH,SAAS,IAAM,oBAAoB,SAAS,EAAE;kCAE9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8B,SAAS,IAAI;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAA2B,SAAS,WAAW;;;;;;sDAC5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDACb,SAAS,aAAa;wDAAC;;;;;;;8DAE1B,8OAAC;oDAAO,WAAU;8DAAwG;;;;;;;;;;;;;;;;;;;;;;;;uBAb3H,SAAS,EAAE;;;;;gBAqBtB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX"}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "file": "target.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/target.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Target\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Target = createLucideIcon('target', __iconNode);\n\nexport default Target;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "file": "book-open.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/book-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 7v14', key: '1akyts' }],\n  [\n    'path',\n    {\n      d: 'M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z',\n      key: 'ruj8y',\n    },\n  ],\n];\n\n/**\n * @component @name BookOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgN3YxNCIgLz4KICA8cGF0aCBkPSJNMyAxOGExIDEgMCAwIDEtMS0xVjRhMSAxIDAgMCAxIDEtMWg1YTQgNCAwIDAgMSA0IDQgNCA0IDAgMCAxIDQtNGg1YTEgMSAwIDAgMSAxIDF2MTNhMSAxIDAgMCAxLTEgMWgtNmEzIDMgMCAwIDAtMyAzIDMgMyAwIDAgMC0zLTN6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpen = createLucideIcon('book-open', __iconNode);\n\nexport default BookOpen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file://C%3A/Users/<USER>/Documents/augment-projects/IQ%20Test/iq-test-app/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}